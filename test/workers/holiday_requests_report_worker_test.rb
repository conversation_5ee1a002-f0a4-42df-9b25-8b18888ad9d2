require 'test_helper'

class HolidayRequestsReportWorkerTest < ActiveSupport::TestCase
  test 'sends a holiday requests report mail for the previous month' do
    time = Time.zone.now
    Timecop.freeze(time) do
      year = time.year
      month = time.month

      assert_difference('ActionMailer::Base.deliveries.count') do
        mail = HolidayRequestsReportWorker.new.perform
        assert_includes mail.subject, "#{year}-#{month}"
      end
    end
  end
end
