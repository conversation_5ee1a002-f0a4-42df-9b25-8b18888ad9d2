require 'test_helper'

module Searches
  class DeletionRequestSearchTest < ActiveSupport::TestCase
    let(:scope) { DeletionRequest.includes(:company) }
    let(:artegence_request) { deletion_requests(:registered_request) }
    let(:efigence_request) { deletion_requests(:processing_request) }
    let(:completed_request) { deletion_requests(:completed_request) }

    test 'returns all records when no filters are applied' do
      search = DeletionRequestSearch.new(scope: scope, filters: {})
      results = search.results.to_a

      assert_equal results.size, DeletionRequest.all.count
      assert_includes results.map(&:id), artegence_request.id
      assert_includes results.map(&:id), efigence_request.id
      assert_includes results.map(&:id), completed_request.id
    end

    test 'filter by term searches in the description' do
      search = DeletionRequestSearch.new(scope: scope, filters: { term: 'John Doe' })
      results = search.results.to_a

      assert_equal 1, results.size
      assert_equal artegence_request.id, results.first.id

      search = DeletionRequestSearch.new(scope: scope, filters: { term: 'GDPR' })
      results = search.results.to_a

      assert_equal 1, results.size
      assert_equal efigence_request.id, results.first.id

      search = DeletionRequestSearch.new(scope: scope, filters: { term: 'deletion' })
      results = search.results.to_a

      assert_operator results.size, :>=, 2
      assert_includes results.map(&:id), deletion_requests(:completed_request).id
      assert_includes results.map(&:id), deletion_requests(:processing_request).id
    end

    test 'filter by company_id returns matching records' do
      artegence_company_id = companies(:two).id
      efigence_company_id = companies(:one).id

      search = DeletionRequestSearch.new(scope: scope, filters: { company_id: artegence_company_id })
      results = search.results.to_a

      assert_operator results.size, :>=, 2
      assert_includes results.map(&:id), artegence_request.id
      assert_includes results.map(&:id), completed_request.id
      assert_not_includes results.map(&:id), efigence_request.id

      search = DeletionRequestSearch.new(scope: scope, filters: { company_id: efigence_company_id })
      results = search.results.to_a

      assert_operator results.size, :>=, 1
      assert_includes results.map(&:id), efigence_request.id
      assert_not_includes results.map(&:id), artegence_request.id
    end

    test 'sort by id works correctly' do
      search = DeletionRequestSearch.new(scope: scope, filters: { sort: 'id asc' })
      results = search.results.to_a

      assert_equal DeletionRequest.order(id: :asc).pluck(:id), results.map(&:id)

      search = DeletionRequestSearch.new(scope: scope, filters: { sort: 'id desc' })
      results = search.results.to_a

      assert_equal DeletionRequest.order(id: :desc).pluck(:id), results.map(&:id)
    end

    test 'sort by description works correctly' do
      search = DeletionRequestSearch.new(scope: scope, filters: { sort: 'description asc' })
      results = search.results.to_a

      assert_equal DeletionRequest.order(description: :asc).pluck(:id), results.map(&:id)

      search = DeletionRequestSearch.new(scope: scope, filters: { sort: 'description desc' })
      results = search.results.to_a

      assert_equal DeletionRequest.order(description: :desc).pluck(:id), results.map(&:id)
    end

    test 'sort by submission_date works correctly' do
      search = DeletionRequestSearch.new(scope: scope, filters: { sort: 'submission_date asc' })
      results = search.results.to_a

      assert_equal DeletionRequest.order(submission_date: :asc).pluck(:id), results.map(&:id)

      search = DeletionRequestSearch.new(scope: scope, filters: { sort: 'submission_date desc' })
      results = search.results.to_a

      assert_equal DeletionRequest.order(submission_date: :desc).pluck(:id), results.map(&:id)
    end

    test 'sort by state works correctly' do
      search = DeletionRequestSearch.new(scope: scope, filters: { sort: 'state asc' })
      results = search.results.to_a

      assert_equal DeletionRequest.order(state: :asc).pluck(:state), results.map(&:state)

      search = DeletionRequestSearch.new(scope: scope, filters: { sort: 'state desc' })
      results = search.results.to_a

      assert_equal DeletionRequest.order(state: :desc).pluck(:state), results.map(&:state)
    end

    test 'sort by company works correctly' do
      search = DeletionRequestSearch.new(scope: scope, filters: { sort: 'company asc' })
      results = search.results.to_a

      expected_order = DeletionRequest.joins(:company).order('companies.name asc').pluck(:id)
      assert_equal expected_order, results.map(&:id)

      search = DeletionRequestSearch.new(scope: scope, filters: { sort: 'company desc' })
      results = search.results.to_a

      expected_order = DeletionRequest.joins(:company).order('companies.name desc').pluck(:id)
      assert_equal expected_order, results.map(&:id)
    end

    test 'handles invalid sort parameters gracefully' do
      search = DeletionRequestSearch.new(scope: scope, filters: { sort: 'invalid_field asc' })
      results = search.results.to_a

      assert_equal results.size, DeletionRequest.all.count

      search = DeletionRequestSearch.new(scope: scope, filters: { sort: 'description invalid_direction' })
      results = search.results.to_a

      assert_equal DeletionRequest.order(description: :asc).pluck(:id), results.map(&:id)
    end

    test 'handles empty and nil filters gracefully' do
      search = DeletionRequestSearch.new(scope: scope, filters: {})
      results = search.results.to_a

      assert_equal results.size, DeletionRequest.all.count

      search = DeletionRequestSearch.new(scope: scope, filters: nil)
      results = search.results.to_a

      assert_equal results.size, DeletionRequest.all.count
    end

    test 'search includes necessary associations' do
      search = DeletionRequestSearch.new(scope: scope, filters: { sort: 'company asc' })
      results = search.results.to_a

      assert_not_nil results.first.association(:company).loaded?
    end
  end
end
