require 'test_helper'

class PaymentSchedulesMailerTest < ActionMailer::TestCase
  before do
    @user = users(:milosz)
  end

  test 'payment schedule required' do
    project = projects(:one)
    email = PaymentSchedulesMailer.create_schedule_reminder(project,
                                                            [@user.email])
    assert_match project.name, email.to_s
  end

  test 'payment schedule edited' do
    payment_schedule = payment_schedules(:project_one_schedule)

    email = PaymentSchedulesMailer.payment_schedule_edited(payment_schedule,
                                                           [@user.email])
    assert_match payment_schedule.project.name, email.to_s
  end
end
