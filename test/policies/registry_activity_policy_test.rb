require 'test_helper'

class RegistryActivityPolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL

  let(:record_model) { RegistryActivity }
  let(:registry_activity) { registry_activities(:one) }

  let(:nil_user) { nil }

  let(:admin_user) do
    user = User.create(
      email: '<EMAIL>',
      username: 'admin_user',
      first_name: 'Admin',
      last_name: 'User',
      password: generate_valid_password,
      password_confirmation: generate_valid_password,
      activates_on: Time.zone.today,
      company: companies(:one)
    )
    user.global_roles << global_roles(:global_admin)
    user
  end

  let(:regular_user) do
    user = User.create(
      email: '<EMAIL>',
      username: 'regular_user',
      first_name: 'Regular',
      last_name: 'User',
      password: generate_valid_password,
      password_confirmation: generate_valid_password,
      activates_on: Time.zone.today,
      company: companies(:one)
    )
    user.global_roles << global_roles(:global_user)
    user
  end

  def test_scope
    assert_equal RegistryActivity.all.to_a.sort, RegistryActivityPolicy::Scope.new(admin_user, RegistryActivity).resolve.to_a.sort
    assert_not_empty RegistryActivityPolicy::Scope.new(admin_user, RegistryActivity).resolve.to_a

    assert_empty RegistryActivityPolicy::Scope.new(regular_user, RegistryActivity).resolve.to_a
    assert_empty RegistryActivityPolicy::Scope.new(nil_user, RegistryActivity).resolve.to_a
  end

  def test_index
    assert_permit admin_user, record_model, :index?
    refute_permit regular_user, record_model, :index?
    refute_permit nil_user, record_model, :index?
  end

  def test_show
    assert_permit admin_user, registry_activity, :show?
    refute_permit regular_user, registry_activity, :show?
    refute_permit nil_user, registry_activity, :show?
  end

  def test_new
    assert_permit admin_user, record_model, :new?
    refute_permit regular_user, record_model, :new?
    refute_permit nil_user, record_model, :new?
  end

  def test_create
    assert_permit admin_user, registry_activity, :create?
    refute_permit regular_user, registry_activity, :create?
    refute_permit nil_user, registry_activity, :create?
  end

  def test_update
    assert_permit admin_user, registry_activity, :update?
    refute_permit regular_user, registry_activity, :update?
    refute_permit nil_user, registry_activity, :update?
  end

  def test_destroy
    assert_permit admin_user, registry_activity, :destroy?
    refute_permit regular_user, registry_activity, :destroy?
    refute_permit nil_user, registry_activity, :destroy?
  end

  def test_activate
    assert_permit admin_user, registry_activity, :activate?
    refute_permit regular_user, registry_activity, :activate?
    refute_permit nil_user, registry_activity, :activate?
  end

  def test_close
    assert_permit admin_user, registry_activity, :close?
    refute_permit regular_user, registry_activity, :close?
    refute_permit nil_user, registry_activity, :close?
  end
end
