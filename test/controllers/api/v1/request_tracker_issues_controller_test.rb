require "test_helper"

module Api
  module V1
    class RequestTrackerIssuesControllerTest < ActionController::TestCase
      setup do
        @user = users(:mkalita_user)
        authenticate(@user)
      end

      test 'sends mail when all required fields filled in' do
        assert_difference('ActionMailer::Base.deliveries.count', 1) do
          post :create, params: { mail_props: { title: 'title',
                                                cc: '<EMAIL>, <EMAIL>',
                                                project_id: projects(:two).id,
                                                content: 'test' } }, format: :json
        end
      end

      test 'does not send mail without title' do
        assert_difference('ActionMailer::Base.deliveries.count', 0) do
          post :create, params: { mail_props: { title: '',
                                                cc: '',
                                                project_id: projects(:two).id,
                                                content: 'test' } }, format: :json
        end
      end

      test 'does not send mail without project_id' do
        assert_difference('ActionMailer::Base.deliveries.count', 0) do
          post :create, params: { mail_props: { title: 'title',
                                                cc: '',
                                                project_id: '',
                                                content: 'test' } }, format: :json
        end
      end

      test 'does not send mail without content' do
        assert_difference('ActionMailer::Base.deliveries.count', 0) do
          post :create, params: { mail_props: { title: 'title',
                                                cc: '',
                                                project_id: projects(:two).id,
                                                content: '' } }, format: :json
        end
      end

      test 'does not send mail with invails mails in cc' do
        assert_difference('ActionMailer::Base.deliveries.count', 0) do
          post :create, params: { mail_props: { title: 'title',
                                                cc: '<EMAIL>, aaa',
                                                project_id: projects(:two).id,
                                                content: '' } }, format: :json
        end
      end
    end
  end
end
