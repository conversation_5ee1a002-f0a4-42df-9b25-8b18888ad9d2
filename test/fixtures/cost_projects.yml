# Read about fixtures at
# http://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

wiktoria_cost_project:
  cost_invoice: wiktoria_cost_invoice
  accounting_number: one
  amount: 100

wiktoria_draft_cost_project:
  cost_invoice: wiktoria_draft_cost_invoice
  accounting_number: one
  amount: 2000

wilhelm_cost_project:
  cost_invoice: wilhelm_pending_department_cost_invoice
  accounting_number: one
  amount: 8_000

dms_cost_invoice_project_one_department_two:
  cost_invoice: dms_cost_invoice_project
  accounting_number: one
  department: two
  amount: 5_000
  cost_account_number: one

dms_cost_invoice_project_one_department_mkalita_department:
  cost_invoice: dms_cost_invoice_project
  accounting_number: one
  department: mkalita_department
  amount: 5_000
  cost_account_number: one

dms_pending_cost_invoice_two_project_one_department_two:
  cost_invoice: dms_pending_cost_invoice_two
  accounting_number: one
  department: two
  amount: 10_000
  cost_account_number: one

dms_pending_cost_invoice_project_one_department_two:
  cost_invoice: dms_pending_cost_invoice
  accounting_number: two
  department: two
  amount: 10_000
  cost_account_number: one
