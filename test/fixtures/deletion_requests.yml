# Read about fixtures at http://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

registered_request:
  full_name: '<PERSON>'
  description: 'Request to delete personal data for <PERSON>'
  taken_actions: 'Removed from all internal systems and databases'
  created_by: wiktoria
  company: two
  submission_date: <%= Date.current + 1.day %>
  comment: 'Initial request submitted'
  state: 0

processing_request:
  full_name: '<PERSON>'
  description: 'GDPR deletion request for <PERSON>'
  taken_actions: 'Data removal in progress across all systems'
  created_by: milosz
  company: one
  submission_date: <%= Date.current - 1.day %>
  comment: 'Processing started'
  state: 1

completed_request:
  full_name: '<PERSON>'
  description: 'Complete data deletion request'
  taken_actions: 'All personal data has been permanently removed'
  created_by: wiktoria
  company: two
  submission_date: <%= Date.current - 7.days %>
  comment: 'Deletion completed successfully'
  state: 2

with_projects:
  full_name: '<PERSON>'
  description: 'Deletion request with associated projects'
  taken_actions: 'Removed from project systems and general databases'
  created_by: wiktoria
  company: two
  submission_date: <%= Date.current - 2.days %>
  comment: 'Request involves multiple projects'
  state: 0

minimal_request:
  taken_actions: 'Basic data removal completed'
  created_by: wiktoria
  company: one
  submission_date: <%= Date.current %>
  state: 0

no_creator_request:
  full_name: 'System Generated Request'
  description: 'Automated deletion request'
  taken_actions: 'System-initiated data cleanup'
  created_by: wiktoria
  company: one
  submission_date: <%= Date.current - 3.days %>
  comment: 'No specific creator assigned'
  state: 0
