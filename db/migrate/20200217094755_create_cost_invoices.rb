class CreateCostInvoices < ActiveRecord::Migration[4.2]
  def change
    create_table :cost_invoices do |t|
      t.string :title
      t.date :sell_date
      t.date :due_date
      t.date :invoice_date
      t.text :description
      t.references :company, index: true, foreign_key: true
      t.references :contractor, index: true, foreign_key: true
      t.integer :state

      t.timestamps null: false
    end
  end
end
