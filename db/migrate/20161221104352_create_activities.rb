# Migration responsible for creating a table with activities
class CreateActivities < ActiveRecord::Migration[4.2]
  # Create table
  def self.up
    create_table :activities do |t|
      t.belongs_to :trackable, polymorphic: true
      t.belongs_to :owner, polymorphic: true
      t.string  :key
      t.text    :parameters # serialized, hard to search
      t.belongs_to :recipient, polymorphic: true
      # NOTE: tracked some_searchable_activity_value: -> { |_controller, model| model.some_searchable_activity_value }
      # t.string :some_searchable_activity_value
      t.string :trackable_title

      t.timestamps
    end

    PublicActivity::Activity.reset_column_information
    # MySQL
    change_column :activities, :trackable_type, :string, limit: 100
    change_column :activities, :owner_type, :string, limit: 100
    change_column :activities, :recipient_type, :string, limit: 100

    PublicActivity::Activity.reset_column_information
    add_index :activities, [:trackable_id, :trackable_type]
    add_index :activities, [:owner_id, :owner_type]
    add_index :activities, [:recipient_id, :recipient_type]
    # only suitable, if we would search for historic values
    # add_index :activities, [:some_searchable_activity_value]
    add_index :activities, [:trackable_title]
  end

  # Drop table
  def self.down
    drop_table :activities
  end
end
