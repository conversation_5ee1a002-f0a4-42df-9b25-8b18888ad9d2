class CreateDeletionRequestsTable < ActiveRecord::Migration[7.0]
  def change
    create_table :deletion_requests do |t|
      t.text :full_name
      t.text :description
      t.text :taken_actions, null: false
      t.integer :created_by_id, null: false
      t.integer :company_id, null: false
      t.date :submission_date, null: false
      t.text :comment
      t.integer :state, default: 0, null: false

      t.timestamps
    end

    add_foreign_key :deletion_requests, :users, column: :created_by_id
    add_index :deletion_requests, :created_by_id
    add_foreign_key :deletion_requests, :companies, column: :company_id
    add_index :deletion_requests, :company_id

    create_table :deletion_request_projects do |t|
      t.bigint :deletion_request_id, null: false
      t.integer :project_id, null: false

      t.timestamps
    end

    add_foreign_key :deletion_request_projects, :deletion_requests, column: :deletion_request_id
    add_index :deletion_request_projects, :deletion_request_id
    add_foreign_key :deletion_request_projects, :projects, column: :project_id
    add_index :deletion_request_projects, :project_id
  end
end
