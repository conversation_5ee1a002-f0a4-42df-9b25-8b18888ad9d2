module Api
  module V1
    class DeletionRequestsController < ApiController
      before_action :set_deletion_request, except: %i[index create]
      before_action :authorize_deletion_request
      before_action :skip_policy_scope

      def index
        results = search(scope: DeletionRequest.includes(:company).order(state: :asc), filters: params[:f]).results
        @deletion_requests = paginate(authorize!(results))
      end

      def show; end

      def create
        @deletion_request = DeletionRequest.new(deletion_request_params)
        @deletion_request.created_by = current_user
        if @deletion_request.save
          respond_with @deletion_request
        else
          render json: { errors: @deletion_request.errors.messages }, status: :unprocessable_entity
        end
      end

      def update
        if @deletion_request.update(deletion_request_params)
          respond_with @deletion_request
        else
          render json: { errors: @deletion_request.errors.messages }, status: :unprocessable_entity
        end
      end

      def destroy
        @deletion_request.destroy
        head :no_content
      end

      def processing
        @deletion_request.processing!
        head :no_content
      rescue AASM::InvalidTransition => e
        render json: { errors: ["State transition error: #{e.message}"] }, status: :unprocessable_entity
      end

      def complete
        @deletion_request.completed!
        head :no_content
      rescue AASM::InvalidTransition => e
        render json: { errors: ["State transition error: #{e.message}"] }, status: :unprocessable_entity
      end

      private

      def set_deletion_request
        @deletion_request = DeletionRequest.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: { errors: ['Deletion request not found'] }, status: :not_found
      end

      def deletion_request_params
        params.require(:deletion_request).permit(:full_name, :description, :taken_actions, :company_id, :submission_date,
                                                 :comment, :state, project_ids: [])
      end

      def authorize_deletion_request
        authorize @deletion_request || DeletionRequest
      end

      def search(options = {})
        ::Searches::DeletionRequestSearch.new(search_options(options))
      end
    end
  end
end
