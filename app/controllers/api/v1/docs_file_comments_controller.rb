class Api::V1::DocsFileCommentsController < Api::V1::ApiController
  before_action :find_project
  before_action :find_docs_file
  before_action :authorize
  before_action :skip_policy_scope

  def index
    @docs_file_comments = @docs_file.docs_file_comments.includes(:created_by)
  end

  def create
    docs_file_comment = @docs_file.docs_file_comments.create(
      docs_file_comment_params.merge(created_by: current_user)
    )

    respond_with docs_file_comment, location: [@project, @docs_file, :docs_file_comments]
  end

  private

  def docs_file_comment_params
    params.require(:docs_file_comment).permit(:content)
  end

  def find_project
    @project = policy_scope(Project).find(params[:project_id])
  end

  def find_docs_file
    @docs_file = @project.docs_files.find(params[:docs_file_id])
  end

  def authorize
    super(@project, :docs_files_access?)
  end
end
