# prevent responses other than json which in turn return 403, why?
module Api
  module V1
    module Concerns
      module Handlers
        module Rescues
          extend ActiveSupport::Concern
          include ::Api::V1::Concerns::Handlers::Errors

          # :nocov:

          included do
            # zrabane w railsach, czekamy na release:
            # https://github.com/rails/rails/pull/24158
            rescue_from 'StandardError' do |exception|
              exception_class_name = exception.class.name
              case exception_class_name
              when 'ActiveRecord::DeleteRestrictionError'
                association = exception.message.split.last
                render_api_error(message: "It cannot be removed because of existing #{association} association(s).",
                                 status: :unprocessable_entity)
              when 'ActionController::NotImplemented'
                head :not_implemented
              when 'ActiveRecord::RecordNotFound', 'Mongoid::Errors::DocumentNotFound'
                error_response = { status: :not_found }
                error_response[:message] = if Rails.env.production? && !Rails.configuration.consider_all_requests_local
                                             I18n.t('api.messages.not_found', default: 'Not found').to_s
                                           else
                                             exception.message
                                           end
                render_api_error(error_response)
              when 'ActiveRecord::RecordInvalid', 'ActiveRecord::RecordNotSaved', 'ActiveRecord::Rollback'
                render_api_error(message: exception.message,
                                 status: :unprocessable_entity)
              when 'ActionController::RoutingError', 'AbstractController::ActionNotFound'
                render_api_error(message: 'Invalid path',
                                 status: :not_found)
              when 'ActionController::InvalidAuthenticityToken', 'ActionController::InvalidCrossOriginRequest'
                render_api_error(message: 'Invalid request',
                                 status: :unprocessable_entity)
              when 'ActionDispatch::ParamsParser::ParseError', 'ActionController::BadRequest',
                   'ActionController::ParameterMissing', 'CGI::Session::CookieStore::TamperedWithCookie',
                   'ActionController::UnknownAction'
                error_response = { status: :bad_request }
                error_response[:message] = if Rails.env.production? && !Rails.configuration.consider_all_requests_local
                                             I18n.t('api.messages.bad_request', default: 'Bad request').to_s
                                           else
                                             exception.message
                                           end
                render_api_error(error_response)
              when 'ActionController::MethodNotAllowed', 'ActionController::UnknownHttpMethod'
                render_api_error(message: 'Invalid method',
                                 status: :method_not_allowed)
              when 'Pundit::NotAuthorizedError'
                error_response = { status: :forbidden }
                if Rails.env.production? && !Rails.configuration.consider_all_requests_local
                  error_response[:message] =
                    I18n.t('api.messages.insufficient_permission_level_to_perform_this_action', default: 'Access denied').to_s
                else
                  error_response[:message] = exception.message
                end
                render_api_error(error_response)
              when 'Pundit::NotDefinedError', 'Pundit::AuthorizationNotPerformedError',
                   'Pundit::PolicyScopingNotPerformedError'
                raise
              when 'Pundit::NotAuthorizedError'
                render_api_error(message: I18n.t('api.messages.insufficient_permission_level_to_perform_this_action', default: 'Access denied').to_s,
                                 status: :forbidden)
              else
                render_standard_error(exception)
              end
            end
          end

          def filter_exception_backtrace(exception)
            exception.backtrace.to_a.slice(0, 20).map { |i| i.gsub(Rails.root.to_s, '[FILTERED]') }
          end

          def render_standard_error(exception)
            if Rails.env.production?
              ::ExceptionNotifier.notify_exception(
                exception,
                data: { path: request.filtered_path, params: request.filtered_parameters }
              )
            end
            raise exception, [exception.message, filter_exception_backtrace(exception)].join("\n ")
          end

          # :nocov:
        end
      end
    end
  end
end
