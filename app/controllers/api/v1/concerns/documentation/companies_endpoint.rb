module Api
  module V1
    module Concerns
      module Documentation
        module CompaniesEndpoint
          include Swagger::Blocks
          extend ActiveSupport::Concern

          included do
            swagger_path '/api/companies' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              operation :get do
                key :description, 'Provides a list of companies based on the search criteria'
                key :operationId, 'getCompanies'
                key :tags, [
                  'companies'
                ]
                parameter do
                  key :name, 'f[term]'
                  key :in, :query
                  key :description, 'filter by term'
                  key :required, false
                  key :type, :string
                end
                parameter do
                  key :name, 'f[sort]'
                  key :in, :query
                  key :description, 'sort by field in direction'
                  key :required, false
                  key :type, :string
                  key :enum, ['id asc', 'id desc', 'name asc', 'name desc', 'created_at asc', 'created_at desc', 'updated_at asc', 'updated_at desc']
                end
                parameter do
                  key :name, 'page'
                  key :in, :query
                  key :description, 'page number'
                  key :required, false
                  key :type, :integer
                end
                parameter do
                  key :name, 'per_page'
                  key :in, :query
                  key :description, 'items per page'
                  key :required, false
                  key :type, :integer
                end
                response 200 do
                  key :description, 'company list response'
                  schema do
                    key :type, :array
                    items do
                      key :'$ref', :CompanyResponses
                    end
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
              operation :post do
                key :description, 'Creates a new company. Duplicates are not allowed'
                key :operationId, 'postCompany'
                key :tags, [
                  'companies'
                ]
                # wazne przy postach
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, :payload
                  key :in, :body
                  key :description, <<-HEREDOC.gsub(/(?:\n\r?|\r\n?)/, '<br>').squish.html_safe
                                    JSON form data for Company to add
                                    Editable by ALL:

                                    Editable only by ADMIN:
                                    name, domain

                                    HEREDOC
                  key :required, true
                  schema do
                    key :'$ref', :CompanyRequest
                  end
                end
                response 200 do
                  key :description, 'company response'
                  schema do
                    key :'$ref', :CompanyResponse
                  end
                end
                response 201 do
                  key :description, 'Response with company'
                  schema do
                    key :"$ref", :CompanyResponse
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response 422 do
                  key :description, 'Unprocessable entity'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
            swagger_path '/api/companies/{id}' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              parameter do
                key :name, :id
                key :in, :path
                key :description, 'ID of company'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              operation :get do
                key :description, 'Returns a company based on a single ID'
                key :operationId, 'getCompany'
                key :tags, [
                  'companies'
                ]
                response 200 do
                  key :description, 'company response'
                  schema do
                    key :'$ref', :CompanyResponse
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
                # security api_key: []
                # security do
                #   key :petstore_auth, ['write:pets', 'read:pets']
                # end
              end
              operation :patch do
                key :description, 'Update company'
                key :operationId, 'patchCompany'
                key :tags, [
                  'companies'
                ]
                # wazne przy postach
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, :payload
                  key :in, :body
                  key :description, <<-HEREDOC.gsub(/(?:\n\r?|\r\n?)/, '<br>').squish.html_safe
                                    JSON form data for Company to update; pass [""] as ids to delete associations
                                    Editable by ALL:

                                    Editable only by ADMIN:
                                    name

                                    HEREDOC
                  key :required, true
                  schema do
                    key :'$ref', :CompanyRequest
                  end
                end
                response 200 do
                  key :description, 'Response with company'
                  schema do
                    key :"$ref", :CompanyResponse
                  end
                end
                response 204 do
                  key :description, 'company updated'
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
              end
              operation :delete do
                key :description, 'deletes a single company based on the ID supplied'
                key :operationId, 'deleteCompany'
                key :tags, [
                  'companies'
                ]
                response 204 do
                  key :description, 'company deleted'
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
          end
        end
      end
    end
  end
end
