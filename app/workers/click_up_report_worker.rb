require 'sidekiq-scheduler'

class ClickUpReportWorker
  include Sidekiq::Worker

  def perform # rubocop:disable Metrics/CyclomaticComplexity, Metrics/AbcSize, Metrics/PerceivedComplexity, Metrics/MethodLength
    differences = []

    fetch_projects_with_click_up_sync.find_each do |project|
      local_users = project.memberships.map(&:member).select { |u| u.is_a?(User) }
      users_emails = local_users.map(&:email)

      click_up_project = fetch_folder(project.click_up_id)
      click_up_users = click_up_project['lists'].flat_map { |list| fetch_members(list['id']) }
      click_up_users_emails = click_up_users.map { |u| u['email'] }

      name_diff = project.name != click_up_project['name']
      archived_diff = project.archived? != click_up_project['archived']
      missing_imp_users = local_users.reject { |u| click_up_users_emails.include?(u.email) }
      missing_cu_users = click_up_users.reject { |u| users_emails.include?(u['email']) }

      next unless name_diff || archived_diff || missing_imp_users.present? || missing_cu_users.present?

      differences << {
        name: project.name,
        name_diff: name_diff,
        archived_diff: archived_diff,
        missing_imp_users: missing_imp_users.map(&:full_name).join(', '),
        missing_cu_users: missing_cu_users.map { |u| u['username'] }.join(', ')
      }
    end

    ClickUpMailer.difference_notification(differences.compact).deliver_now if differences.compact.present?
  end

  private

  def fetch_projects_with_click_up_sync
    Project.active
           .includes(:accounting_number, :company)
           .where.not(accounting_number: { number: 0 })
           .where.not(click_up_id: nil)
           .where(companies: { click_up_synchronization: true })
  end

  def fetch_folder(id)
    ClickUpApi.get("/folder/#{id}")
  end

  def fetch_members(id)
    ClickUpApi.get("/list/#{id}/member")['members']
  end
end
