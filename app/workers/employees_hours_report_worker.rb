class EmployeesHoursReportWorker
  include Sidekiq::Worker

  def perform(employees_report_id)
    employees_report = EmployeesReport.find(employees_report_id)
    employees_report.generate

    path = Settings.invoices_integration.files_path
    file_name = employees_report.file_name

    File.open(File.join(path, file_name), 'wb') do |file|
      file.write(employees_report.file.read)
    end

    SMBWorker.perform_async(file_name, File.join(employees_report.company.name, 'KADRY'),
                            'estelligence_share')
    SMBWorker.perform_async(file_name, File.join(employees_report.company.name, 'KADRY'),
                            'bi_share')
  end
end
