require 'sidekiq-scheduler'

class UserEntryCardsWorker
  include Sidekiq::Worker

  def perform
    csv = UserEntryCardsCsvGenerator.generate
    path = Settings.invoices_integration.files_path
    timestamp = Time.zone.today.strftime('%Y%m%d')
    file_name = "entry_cards_#{timestamp}.csv"

    File.write(File.join(path, file_name), csv)

    SMBWorker.perform_async(file_name, 'entry_cards', 'bi_share')
  end
end
