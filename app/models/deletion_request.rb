class DeletionRequest < ApplicationRecord
  include AASM

  belongs_to :created_by, class_name: 'User', optional: true
  belongs_to :company

  has_many :deletion_request_projects, dependent: :destroy
  has_many :projects, through: :deletion_request_projects

  validates :taken_actions, :company_id, :submission_date, presence: true

  accepts_nested_attributes_for :projects

  enum state: { registered: 0, processing: 1, completed: 2 }

  aasm column: :state, enum: true do
    state :registered, initial: true
    state :processing
    state :completed

    event :processing do
      transitions from: :registered, to: :processing
    end

    event :complete do
      transitions from: :processing, to: :completed
    end
  end
end
