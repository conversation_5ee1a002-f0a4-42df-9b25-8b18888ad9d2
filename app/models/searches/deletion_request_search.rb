module Searches
  class DeletionRequestSearch
    include SearchObject.module(:model)
    include Sanitizers
    include ::Searches::Concerns::Sorting

    SORTABLE_FIELDS = %w[id description submission_date state company].freeze

    def self.sort_by
      SORTABLE_FIELDS
    end

    option :term do |scope, value|
      sql = <<-SQL
        deletion_requests.description LIKE :term
      SQL
      scope.where sql, term: escape_search_term(value)
    end

    option :company_id do |scope, value|
      scope.where(company_id: value)
    end

    option :sort do |scope, value|
      key, direction = value.split
      next scope unless SORTABLE_FIELDS.include?(key)

      direction = 'asc' unless %w[asc desc].include?(direction)

      case key
      when 'company'
        scope.reorder("companies.name #{direction}")
      else
        scope.reorder("#{scope.model.table_name}.#{key} #{direction}")
      end
    end
  end
end
