class User < ApplicationRecord
  RM_ATTRIBUTES = %i[first_name last_name username email state redmine company_id
                     position_id time_reports_not_required].freeze
  CU_ATTRIBUTES = %i[email].freeze

  AGREEMENTS_ATTRIBUTES = %i[company_id department_id contract_of_employment].freeze

  include Documentation::UserModel

  # notice this comes BEFORE the include statement below
  # also notice that :registerable is not included in this block
  devise :database_authenticatable, :recoverable, :rememberable, :trackable,
         :validatable, :confirmable, :lockable, :password_expirable,
         :password_archivable
  # NOTE: that this include statement comes AFTER the devise block above
  include DeviseTokenAuth::Concerns::User
  include UserDevisePatch
  include UserDeviseTokenAuthPatch
  include UserLdapEntryable if Settings.ldap_provider.present?
  include LdapPasswordable
  include AbsenceQuotable
  include UserValidations

  has_paper_trail only: %I[first_name last_name username email company_id state absence_quota cloud
                           redmine chat monitoring svn position_id sick_absence_quota
                           time_reports_not_required contract_of_employment absence_balance part_time
                           activity_validation_disabled],
                  on: %i[create update destroy]

  scope :internal, -> { where.not(company_id: nil) }
  scope :internal_native_company, -> { where(users: { company_id: -> { Company.native.ids }.call }) }
  scope :native, -> { joins(:company).where(companies: { name: Company::NATIVE }) }

  scope :filter_by, proc { |params|
    Finders::UserFinder.new(self).call(params)
  }

  validate :active_assets?

  belongs_to :department, touch: true, optional: true
  belongs_to :company, touch: true, optional: true
  belongs_to :position, optional: true

  has_one :contractor, dependent: :destroy
  has_many :created_evaluations, class_name: 'Evaluation', foreign_key: 'created_by_id',
                                 inverse_of: 'created_by', dependent: :nullify
  has_many :evaluations, dependent: :destroy
  has_many :survey_answers, dependent: :nullify
  has_many :accounting_numbers, dependent: :nullify

  has_many :assets
  has_one :bio

  has_many :notifications, dependent: :delete_all
  has_many :combined_notifications, as: :subject,
                                    dependent: :delete_all,
                                    after_remove: [:touch_self],
                                    after_add: [:touch_self],
                                    class_name: 'Notification',
                                    inverse_of: :subject
  has_many :absences, dependent: :delete_all
  has_many :applicant_holiday_requests, class_name: 'HolidayRequest', foreign_key: 'applicant_id', inverse_of: :applicant
  has_many :examiner_holiday_requests, class_name: 'HolidayRequest', foreign_key: 'examiner_id', inverse_of: :examiner

  has_many :departments_as_uber_chief, class_name: 'Department', foreign_key: 'uber_chief_id', inverse_of: :uber_chief
  has_many :departments_as_chief, class_name: 'Department', foreign_key: 'chief_id', inverse_of: :chief
  has_many :departments_as_substitute_chief, class_name: 'Department',
                                             foreign_key: 'substitute_chief_id',
                                             inverse_of: :substitute_chief
  has_many :departments_as_supervisor, class_name: 'Department', foreign_key: 'supervisor_id', inverse_of: :supervisor
  has_many :public_keys, dependent: :destroy
  has_many :wifi_tokens
  has_many :email_verses, as: :email_recipient, inverse_of: :email_recipient
  has_many :email_aliases, dependent: :destroy, autosave: true
  has_many :approvals
  has_many :cost_invoices, dependent: :nullify
  has_many :cost_invoice_acceptances, dependent: :nullify
  has_many :cost_allocation_templates, dependent: :destroy
  has_many :employees_reports, dependent: :destroy
  has_many :cards
  has_many :remote_work_periods, dependent: :destroy
  has_many :user_entry_cards, dependent: :destroy
  has_many :user_contracts, dependent: :destroy
  has_many :created_registry_activities, class_name: 'RegistryActivity', foreign_key: 'created_by_id',
                                         inverse_of: 'created_by', dependent: :nullify
  has_many :created_deletion_requests, class_name: 'DeletionRequest', foreign_key: 'created_by_id',
                                       inverse_of: 'created_by', dependent: :nullify
  has_one :current_contract, -> { overlapping(Date.current, Date.current) },
          class_name: 'UserContract', inverse_of: :user
  has_one :current_entry_card, -> { where(ends_on: nil).or(where(starts_on: ..Time.current, ends_on: Time.current..)) },
          class_name: 'UserEntryCard',
          inverse_of: :user
  has_many :resource_time_entries, dependent: :destroy
  has_many :training_requests, dependent: :destroy
  has_many :calendar_months, dependent: :destroy

  delegate :board_member?, :board_member, to: :department, allow_nil: true
  delegate :mpk_number, :mpk_number_id, to: :department, allow_nil: true

  before_validation :set_generated_password_if_new_record
  before_save :service_password, :delete_service_password
  before_create :set_dismiss_onboarding
  after_create :expire_first_password
  before_update :notify_responsible, if: -> { state_changed? && locked? }
  before_update :update_random_password, if: -> { state_changed? && locked? }
  before_update :revoke_tokens, if: -> { state_changed? && locked? }
  after_destroy :lock_redmine, if: :redmine?
  after_save :touch_groups_and_projects, if: -> { saved_change_to_attribute?(:state) }
  after_save :update_redmine
  after_save :update_click_up, if: -> { company&.click_up_synchronization? && !click_up_id? }
  after_commit :update_approvals, if: :persisted?
  after_update :reject_applicant_holiday_requests, if: -> { saved_change_to_state?(from: 'active', to: 'locked') }

  has_many :group_memberships, dependent: :delete_all
  # Notice that after_destroy callbacks will not be triggered on the associated entity when using has_many :through
  # If the :through option is true callbacks in the join models are triggered except destroy callbacks, since deletion is direct.
  has_many :groups, through: :group_memberships,
                    after_remove: %i[remove_members_in_projects_with_group
                                     touch_self touch_group],
                    after_add: %i[add_members_in_projects_with_group
                                  touch_self touch_group]
  has_many :user_global_roles, dependent: :delete_all
  # Notice that after_destroy callbacks will not be triggered on the associated entity when using has_many :through
  # If the :through option is true callbacks in the join models are triggered except destroy callbacks, since deletion is direct.
  has_many :global_roles, through: :user_global_roles,
                          after_remove: %i[touch_self touch_global_role],
                          after_add: %i[touch_self touch_global_role]

  has_many :memberships, as: :member, dependent: :destroy, inverse_of: :member
  has_many :roles, through: :memberships

  has_many :group_roles, through: :groups, source: :roles

  # rubocop:disable Rails/InverseOf
  has_many :access_tokens,
           class_name: 'Doorkeeper::AccessToken',
           foreign_key: :resource_owner_id,
           dependent: :delete_all # or :destroy if you need callbacks
  # rubocop:enable Rails/InverseOf

  attr_accessor :dont_generate_password, :impersonator_id

  enum state: { active: 0, locked: 1 }

  serialize :preferences, Hash

  scope :board_members, -> { joins(:department).where(departments: { board_member: true }) }

  scope :for_project, lambda { |project_id|
    joins('
      LEFT JOIN `group_memberships`
        ON `group_memberships`.user_id = `users`.id
      LEFT JOIN `groups`
        ON `groups`.id = `group_memberships`.group_id
      INNER JOIN `memberships`
        ON (`memberships`.member_id = `users`.id
          AND `memberships`.member_type = "User")
        OR (`memberships`.member_id = `groups`.id
          AND `memberships`.member_type = "Group")')
      .where(memberships: { project_id: project_id })
  }

  scope :project_pms, lambda { |project_id|
    joins(memberships: :roles).where(roles: { pm: true },
                                     memberships: { project_id: project_id }).distinct
  }

  scope :project_pms_and_accounters, lambda { |project_id|
    joins(memberships: :roles).where(
      'roles.responsible = 1 OR roles.pm = 1'
    ).where(memberships: { project_id: project_id }).distinct
  }

  accepts_nested_attributes_for :cards

  # allows user to change password without current_password
  attr_writer :allow_password_change

  def allow_password_change
    @allow_password_change || false
  end

  def involved_in_projects
    Project.joins(memberships: :roles).where(memberships: { member: self }).distinct
  end

  def projects_for_holiday_notification
    involved_in_projects.where(roles: { send_holiday_request_notification: true }).distinct
  end

  # A has_many relationship must have injective connections at least in one direction,
  # so an "OR" in a SQL clause makes no sense.
  # How should a CREATE operation decide which condition to satisfy to create a new record?
  # This relationship is read only by definition and so it is not a has_many relationship.
  def departments_as_chief_or_substitute_chief
    Department.in_chief_or_substitute_chief(id)
  end

  def group_ids=(*args, &)
    @original_group_ids = Set.new(group_ids)
    super
  end

  def roles_on(project) # rubocop:disable Metrics/AbcSize
    project_groups = groups.includes(memberships: :roles)
                           .where(memberships: { project_id: project.id })
    return project_groups.map(&:memberships).flatten.map(&:roles).flatten.uniq if project_groups.any?

    memberships.where(project_id: project.id).includes(:roles)
               .map(&:roles).flatten
  end

  def full_name
    [first_name, last_name].reject(&:blank?).join(' ')
  end

  def working_hours(date)
    calendar_month(date.year, date.month).days[date.day].to_i
  end

  def working_day?(date)
    working_hours(date).positive?
  end

  def monthly_working_hours(year, month)
    calendar_month(year, month).total_hours
  end

  def monthly_working_days(year, month)
    (Date.new(year, month, 1)..Date.new(year, month, -1)).count { working_day?(_1) }
  end

  # Jesli jest grudzien i ktos zaklada na styczen to:
  # - nie dostal jeszcze przydzialu dni na nast. rok, wiec warning jest prawidlowy
  # - jesli to urlop 'na zadanie', to sklada go szef w dniu urlopu, wiec current year bedzie prawidlowe,
  #   przy czym jesli jest z wyprzedzeniem skladany request, to nie powinno sie to nazywac NZ tylko zwykly urlop,
  #   wtedy nie bedzie warningu (chyba ze pula urlopowa wyczerpana)
  # - jesli ktos ma zalegle urlopy i chce wziac 30 dni, to o ile nie jest to urlop NZ > 4/rok,
  #   to spokojnie moze byc zaakceptowany np. na przelomie lat (nie ma warningu, bo to jest dopuszczalne)
  def overuse_of_holidays?
    negative_absence_balance? || overuse_of_holidays_nz?
  end

  def overuse_of_holidays_nz?(days = nil)
    year = Time.current.year
    category = HolidayRequest.categories['Niedostępność/Ż']
    applicant_holiday_requests.where
                              .not(accepted_at: nil)
                              .in_year(year)
                              .where(category: category)
                              .map do |hr|
                                hr.business_days_in_year(year).size
                              end
                              .sum + (days || 0) > 4
  end

  def negative_absence_balance?(days = nil)
    absence_balance && absence_balance - (days || 0) < 0
  end

  def sick_absence_balance(holiday_request_id = nil)
    sick_absence_quota - HolidayRequest.where(applicant_id: id, category: HolidayRequest::ABSENCE_SICK)
                                       .where.not(id: holiday_request_id, accepted_at: nil).in_year(Time.current.year)
                                       .sum(&:business_days_in_current_year_count)
  end

  # prevent touching company, because of devise trackable
  def update_tracked_fields!(request) # rubocop:disable Rails/SkipsModelValidations
    update_tracked_fields(request)
    columns_to_update = changes.select do |k, _v|
      Devise::Models::Trackable.required_fields(self.class).include?(k.to_sym)
    end.transform_values(&:second)
    update_columns(columns_to_update) unless columns_to_update.empty?
    clear_changes_information
    true
  end

  def native?
    return false unless company

    Company::NATIVE.include?(company.name)
  end

  def active_assets?
    return unless locked? && assets.any?(&:active?)

    errors.add(:user, 'has active assets')
  end

  def leads?(applicant)
    return if applicant.nil?
    return false if applicant == self

    department = applicant.department
    return false unless department

    leads_as_uber_chief?(department) ||
      leads_as_supervisor?(department) ||
      leads_as_chief?(department, applicant) ||
      leads_as_substitute_chief?(department, applicant)
  end

  def departments
    Department.where(id: department_id)
  end

  def departments=(new_departments)
    self.department = Array.wrap(new_departments).first
  end

  def department_ids
    Array.wrap(department.try(:id))
  end

  def department_ids=(new_department_ids)
    self.department_id = Array.wrap(new_department_ids).first
  end

  def update_random_password
    self.password = self.password_confirmation = SecureRandom.base64(9)
  end

  def update_approvals_cache
    self.has_approvals = approvals.where(accepted: false).any?
    return unless changed?

    save
    if ::PUBLIC
      RedmineUsersWorker.perform_async('update', id)
    else
      RedmineUsersWorker.new.perform('update', id)
    end
  end

  def internal?
    company_id.present?
  end

  def active_contract_of_employment?
    active_contracts.employment.any?
  end

  def active_contracts
    user_contracts.overlapping(Time.zone.today, Time.zone.today)
  end

  def replaced_chiefs
    return @replaced_chiefs if @replaced_chiefs

    users = User.includes(:department)
    users = users.where(id: Department.chief_on_holiday.select(:uber_chief_id))
                 .or(users.where(id: Department.uber_chief_on_holiday.select(:supervisor_id)))
                 .or(users.where(id: Department.uber_chief_on_holiday.select(:substitute_chief_id)))

    @replaced_chiefs = users.select do |user|
      user.self_or_nil == self
    end
  end

  def self_or_nil
    absences.current_absence&.any? ? nil : self
  end

  private

  def leads_as_uber_chief?(department)
    department.uber_chief_id == id
  end

  def leads_as_supervisor?(department)
    department.supervisor_id == id
  end

  def leads_as_chief?(department, applicant)
    department.chief_id == id &&
      department.uber_chief_id != applicant.id &&
      department.supervisor_id != applicant.id
  end

  def leads_as_substitute_chief?(department, applicant)
    department.substitute_chief_id == id &&
      department.uber_chief_id != applicant.id &&
      department.supervisor_id != applicant.id &&
      department.chief_id != applicant.id
  end

  def calendar_month(year, month)
    @calendar_month ||= Hash.new do |hash, (y, m)|
      hash[[y, m]] = calendar_months.find_by(year: y, month: m) || DefaultCalendarMonthGenerator.new(y, m).pl_full_time
    end

    @calendar_month[[year, month]]
  end

  def update_auth_header(token, client = 'default')
    headers = build_auth_header(token, client)
    clean_old_tokens

    # rubocop:disable Rails/SkipsModelValidations
    update_column(:tokens, tokens)
    # rubocop:enable Rails/SkipsModelValidations

    headers
  end

  def set_dismiss_onboarding
    self.dismiss_onboarding = false if company.try(:show_onboarding?)
    true
  end

  def add_members_in_projects_with_group(group)
    group_memberships = group.memberships.includes(:membership_roles)
    return if group_memberships.empty?

    group_memberships.each do |group_membership|
      roles_of_group_in_project = group_membership.membership_roles
      project_id = group_membership.project_id
      membership = memberships.find_by(project_id: project_id)
      membership ||= memberships.build(project_id: project_id)
      add_roles(roles_of_group_in_project, membership)
      membership.save unless new_record?
    end
  end

  def add_roles(roles_of_group_in_project, membership)
    roles_of_group_in_project.each do |membership_role|
      membership.membership_roles << MembershipRole.new(role_id: membership_role.role_id,
                                                        inherited_from_id: membership_role.id)
    end
  end

  def remove_members_in_projects_with_group(group)
    group_membership_role_ids = MembershipRole.joins(:membership)
                                              .where(memberships: { member_id: group, member_type: 'Group' })
                                              .pluck(:id)
    MembershipRole.joins(:membership)
                  .where(memberships: { member_id: self, member_type: 'User' },
                         inherited_from_id: group_membership_role_ids)
                  .destroy_all
  end

  # department
  def touch_department(department)
    department.touch if department.persisted?
  end

  # global_role # removed global_role (delete_all/destroy user_global_roles)
  def touch_global_role(global_role)
    global_role.touch if global_role.persisted?
  end

  # group # removed group (delete_all/destroy group_memberships)
  def touch_group(group)
    group.touch if group.persisted?
  end

  def touch_evaluation_iteration(evaluation_iteration)
    evaluation_iteration.touch if evaluation_iteration.persisted?
  end

  # self # user
  def touch_self(_)
    touch if persisted?
  end

  # rubocop:disable Rails/SkipsModelValidations
  def expire_first_password
    update_column(:password_changed_at, nil)
  end
  # rubocop:enable Rails/SkipsModelValidations

  def set_generated_password_if_new_record
    return unless new_record? && !dont_generate_password

    generated_password = "@1Aa#{Devise.friendly_token.first(8)}"
    self.password = generated_password
    self.password_confirmation = generated_password
    self.first_password = generated_password
  end

  def delete_service_password
    if delete_dev_password == true
      hashed_passwords.delete('dev')
      self.dev_password = nil
      self.delete_dev_password = nil
    end
    return unless delete_owncloud_password == true

    hashed_passwords.delete('owncloud')
    self.owncloud_password = nil
    self.delete_owncloud_password = nil
  end

  # prevent uncaching users, because of devise trackable or rememberable
  def should_record_timestamps?
    (changes.keys.map(&:to_sym) - (Devise::Models::Trackable.required_fields(nil) + %i[remember_token
                                                                                       remember_created_at])).present? && super
  end

  # override default devise_token_auth validation
  def unique_email_user
    true
  end

  def rm_attr_changed?
    RM_ATTRIBUTES.map { |attr| saved_change_to_attribute?(attr) }.any? ||
      (@original_group_ids && @original_group_ids != Set.new(group_ids))
  end

  def agreements_attributes_changed?
    (AGREEMENTS_ATTRIBUTES & previous_changes.keys.map(&:to_sym)).any?
  end

  def update_redmine
    if redmine? && rm_attr_changed?
      RedmineUsersWorker.perform_in(5.seconds, 'update', id)
    elsif !redmine? && saved_change_to_attribute?(:redmine) && redmine_id
      RedmineUsersWorker.perform_in(5.seconds, 'lock', redmine_id)
    end
    @original_group_ids = nil
  end

  def update_click_up
    ClickUpUsersWorker.perform_async('sync_user', id)
  end

  def update_approvals
    return unless agreements_attributes_changed?

    ApprovalsForUserWorker.perform_async(id)
  end

  def lock_redmine
    return unless redmine_id

    RedmineUsersWorker.perform_in(5.seconds, 'lock', redmine_id)
  end

  def touch_groups_and_projects
    groups.each(&:touch)
    memberships.includes(:project).map(&:project).each(&:touch)
  end

  def notify_responsible
    ResponsibleUserLockedWorker.perform_async(id)
  end

  def reject_applicant_holiday_requests
    applicant_holiday_requests.pending.each do |holiday_request|
      holiday_request.assign_attributes(examiner_comment: 'Holiday request rejected because applicant has been locked.',
                                        examiner_comment_author: '[SYSTEM]')
      holiday_request.reject_by!(nil)
    end
  end

  def revoke_tokens
    self.tokens = {}
    Doorkeeper::AccessToken.where(resource_owner_id: id).find_each(&:revoke)
  end
end
