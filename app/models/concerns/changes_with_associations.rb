# http://www.mariocarrion.com/2015/07/01/dirty-associations.html
# what_changed = what_changed?(fancy_model, fancy_model.changes, [:users])
# OR:
# what_changed = what_changed?(fancy_model, fancy_model.previous_changes, [:users])
# if what_changed.changed?
#   if what_changed.has_key?(:things)
#     # I do whatever I have to do... maybe call "broadcast(:on_things_changed)"?
#   end
# end
# OR:
# what_changed_with_associations([:users, :group_memberships, :memberships]).changes
# NOTE: supports has_many, has_many through (https://robots.thoughtbot.com/accepts-nested-attributes-for-with-has-many-through)
module ChangesWithAssociations
  extend ActiveSupport::Concern

  WhatChanged = Struct.new(:object, :changes) do
    def changed?
      self != NoChanges
    end
    delegate :has_key?, to: :changes
  end
  NoChanges = WhatChanged.new(nil, ActiveSupport::HashWithIndifferentAccess.new)

  def what_changed?(object, changes)
    what_changed = nil

    notable_changes = select_notable_changes(object, changes)

    what_changed = WhatChanged.new(object, notable_changes) unless notable_changes.empty?
    what_changed || NoChanges
  end

  private

  def select_notable_changes(object, changes)
    ignored = ['updated_at']
    notable = changes.reject { |key, _val| ignored.include?(key.to_s) }
    notable.merge!(new_record?: true) if object.new_record?
    notable.merge!(destroyed?: true) if object.destroyed?
    notable.merge!(marked_for_destruction?: true) if object.marked_for_destruction?
    notable
  end
end
