class Me < SimpleDelegator
  USER_POLICY_PERMISSION_NAMES = %w[
    global_admin
    privileged_user
    global_admin_programmer
    hr_user
    hr_coordinator_user
    internal_user
    board_member
    native_user
    department_chef
    department_chef_or_uber
    account_manager
  ].freeze

  def my_menu # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/MethodLength, Metrics/PerceivedComplexity
    outcome = {}
    menu_activities = my_activities.to_a.reject { |i| ['memberships'].include?(i) }
    if menu_activities.to_a.select do |i|
         i.match(/projects:index/)
       end.any? || (__getobj__.present? && (global_admin? || privileged_user? || global_admin_programmer?))
      outcome[:projects] = {
        projects: true,
        report: menu_activities.to_a.select { |i| i.match(/projects:report/) }.any?,
        idle: project_policy.idle?,
        files: docs_file_policy.index?
      }
    end
    add_controlling_menu_item(outcome)
    if __getobj__.present?
      outcome[:management] = {
        groups: global_admin? || menu_activities.to_a.select { |i| i.match(/groups:update/) }.any?,
        roles: global_admin? || menu_activities.to_a.select { |i| i.match(/roles:update/) }.any?,
        users: global_admin? || menu_activities.to_a.select { |i| i.match(/users:global_index/) }.any?,
        positions: global_admin? || menu_activities.to_a.select { |i| i.match(/positions:update/) }.any?,
        agreements: global_admin? || menu_activities.to_a.select { |i| i.match(/^agreements:update/) }.any?,
        resources: global_admin? || menu_activities.to_a.select { |i| i.match(/booking_resources:index/) }.any?,
        api_keys: global_admin? || menu_activities.to_a.select { |i| i.match(/api_keys:index/) }.any?,
        departments: global_admin? ||
                     (internal_user? &&
                     (hr_user? &&
                       menu_activities.to_a.select { |i| i.match(/departments:update/) }.any?)),
        clients: menu_activities.to_a.select { |i| i.match(/clients:index/) }.any?,
        contractors: menu_activities.to_a.select { |i| i.match(/contractors:manage/) }.any?
      }
      outcome.delete(:management) unless outcome[:management].map(&:second).any?
      if (global_admin? ||
         (internal_user? &&
           (hr_user? ||
             (!holiday_request_policy.separate_company? &&
               menu_activities.to_a.select do |i|
                 i.match(/holiday_requests:index/) || i.match(/absences:index/)
               end.any?)))) && native_user? && native_user?
        outcome[:holidays] = {
          my_holidays: native_user?,
          team_calendar: native_user?,
          work_calendar: calendar_month_policy.index?,
          requests: holiday_request_policy.requests_index? && native_user?,
          user_list: hr_user? && native_user?
        }
      end
      if native_user? && remote_work_period_policy.index?
        outcome[:remote_work_periods] = {
          my_work_periods: true,
          team_calendar: true,
          requests: board_member? || global_admin? || department_chef_or_uber?
        }
      end
      if native_user? && training_request_policy.index?
        outcome[:trainings] = {
          my_trainings: true,
          requests: board_member? || global_admin? || allowed_activity?('training_requests:global_manage') ||
                    department_chef_or_uber? || department_chef?,
          budgets: allowed_activity?('training_requests:global_manage')
        }
      end
      if internal_user? && native_user?
        outcome[:evaluations] = {}
        outcome[:evaluations][:evaluations] = evaluation_policy.users_index?
        outcome[:evaluations][:survey_answers] = survey_answer_policy.index?
      end
      if native_user? && request_tracker_issue_policy.create?
        outcome[:mail_to_admin] = {
          send_mail: true
        }
      end
      outcome[:invoices] = {
        income_invoices: board_member? || menu_activities.to_a.index('invoices:index') ||
                         menu_activities.to_a.index('payment_schedules:global_show'),
        accounting_numbers: menu_activities.to_a.index('invoices:index'),
        cost_account_numbers: menu_activities.to_a.index('accounting_numbers:manage'),
        cost_invoices: hr_cost_invoice_policy.index?,
        employees_reports: workers_report_policy.index?,
        b2b_reports: workers_report_policy.index?,
        bank_accounts: menu_activities.to_a.index('payment_schedules:global_manage'),
        monthly_reports: invoice_policy.monthly_report?,
        external_costs_monthly_reports: external_cost_policy.index?,
        my_payment_schedules: menu_activities.to_a.index('invoices:index'),
        dashboard: dashboard_policy.show?
      }
      outcome[:invoices] = nil unless outcome[:invoices].values.any?
      outcome[:inventory] = true if menu_activities.to_a.index('inventory_items:manage')
    end

    if global_admin? || global_admin_programmer?
      outcome[:global_settings] = {
        global_roles: true,
        global_agreements_settings: true
      }
    end

    if board_member? || department_chef_or_uber? || hr_user?
      outcome[:applications] = {
        start_stop_procedure: true
      }
    end

    outcome[:requests] = {
      server: internal_user? && native_user?,
      vpn: internal_user? && native_user?,
      domain: internal_user? && native_user?,
      ssl_certificate: internal_user? && native_user?,
      google_servicies: internal_user? && native_user?,
      cms_access: internal_user? && native_user?,
      generic_asset: internal_user? && native_user?,
      my_requests: internal_user? && native_user?,
      kubernetes: internal_user? && native_user?,
      my_projects_assets: internal_user? && menu_activities.to_a.select { |i| i.match(/assets:manage/) }.any? && native_user?,
      manage_request: internal_user? &&
                      (department_chef? || menu_activities.to_a.select { |i| i.match(/assets:manage/) }.any?) &&
                      native_user?,
      assets_order: internal_user? && menu_activities.to_a.select { |i| i.match(/assets:orders/) }.any? && native_user?
    }

    outcome[:bios] = {
      user_list: global_admin? || department_chef? || menu_activities.to_a.select { |i| i.match(/bios:index/) }.any?
    }

    implementation_all_projects = board_member? || global_admin? || menu_activities.to_a.index('statistics:view') ||
                                  department_chef_or_uber?
    any_pm_role = __getobj__.roles.where(roles: { pm: true }, memberships: { project_id: Project.active }).any?
    outcome[:statistics] = {
      regression: menu_activities.to_a.index('statistics:view') || department_chef_or_uber? || board_member?,
      regression_permitted_projects_only: !(global_admin? || menu_activities.to_a.index('projects:global_manage')),
      implementation: implementation_all_projects || any_pm_role,
      implementation_as_pm: !implementation_all_projects && any_pm_role,
      effectiveness: board_member? || global_admin? || department_chef_or_uber?,
      effectiveness_as_chief: !(board_member? || global_admin?) && department_chef_or_uber?,
      utilization: board_member? || global_admin? || project_policy.accounting? || department_chef_or_uber?,
      utilization_as_chief: !(board_member? || global_admin? || project_policy.accounting?) && department_chef_or_uber?
    }
    outcome.delete(:statistics) unless outcome[:statistics].values.any?

    outcome[:logs] = {
      logs_list: menu_activities.to_a.select { |i| i.match(/logs:index/) }.any?
    }
    outcome.delete(:logs) unless outcome[:logs].values.any?

    if menu_activities.to_a.index('dms-cost_invoices:create')
      outcome[:dms] = {
        my_document_list: true,
        new_document: true,
        document_list: menu_activities.to_a.index('dms-cost_invoices:global_manage') ||
                       menu_activities.to_a.index('dms-cost_invoices:global_index') ||
                       department_chef_or_uber? || board_member? || account_manager?
      }
    end

    outcome[:gdpr] = {
      registry_activity: registry_activity_policy.index?,
      deletion_request: deletion_request_policy.index?
    }

    outcome.with_indifferent_access
  end

  # naming format: controller_name:action_name
  # [
  #   'projects:index',
  #   'projects:show',
  #   'projects:create',
  #   'projects:update',
  #   'projects:destroy'
  # ]
  def my_activities
    @my_activities ||= global_roles.map(&:activities).flatten.uniq.sort
  end

  def my_activities_hash
    @my_activities_hash ||= my_activities.each_with_object({}) do |item, h|
      resource, _i, action = item.match(/(^.*)(:)(.*)/i).captures
      h[resource] ||= {}
      h[resource][action] = true
    end
  end

  # this is just a wrapper
  def my_abilities
    {
      my_genre: __getobj__.class.name.underscore,
      my_roles: my_roles,
      my_access: my_access,
      my_menu: my_menu
    }.with_indifferent_access
  end

  # combine the roles, to your liking
  # this is purely for helping to assemble the view layer, like buttons, filters etc.
  # would be great to deprecate this in the future
  def my_roles
    {
      global_admin_programmer: global_admin_programmer?,
      global_admin: global_admin?,
      global_admin_or_privileged_user: global_admin? || privileged_user?,
      global_admin_or_hr_user: global_admin? || hr_user?,
      global_admin_or_hr_coordinator_user: global_admin? || hr_coordinator_user?
    }.with_indifferent_access
  end

  # api permissions
  def my_access
    my_activities_hash.with_indifferent_access
  end

  def my_preferences
    __getobj__.preferences
  end

  def unaccepted_agreements
    has_approvals?
  end

  USER_POLICY_PERMISSION_NAMES.each do |name|
    # @!method global_admin?
    # @!method privileged_user?
    # @!method global_admin_programmer?
    # @!method hr_user?
    # @!method internal_user?
    # @!method board_member?
    define_method("#{name}?") do
      user_policy.public_send("#{name}?")
    end
  end

  def to_param
    @as_param ||= Digest::SHA1.hexdigest({
      user: __getobj__.serializable_hash,
      memberships: __getobj__.membership_ids,
      global_roles: __getobj__.global_role_ids,
      activities: my_activities
    }.flatten.join('-'))
  end

  private

  def add_controlling_menu_item(outcome)
    return unless project_policy.accounting?

    outcome[:projects] ||= {}
    outcome[:projects][:controlling] = true
    outcome[:projects][:employees_report] = true
  end

  def memberships
    @memberships ||= __getobj__.memberships.includes(:roles).to_a
  end

  def global_roles
    @global_roles ||= __getobj__.global_roles.to_a
  end

  def user_policy
    @user_policy ||= UserPolicy.new(__getobj__, User)
  end

  def project_policy
    @project_policy ||= ProjectPolicy.new(__getobj__, Project)
  end

  def evaluation_policy
    @evaluation_policy ||= EvaluationUserPolicy.new(__getobj__, Evaluation)
  end

  def survey_answer_policy
    @survey_answer_policy ||= SurveyAnswerPolicy.new(__getobj__, SurveyAnswer)
  end

  def hr_cost_invoice_policy
    @hr_cost_invoice_policy ||= B2B::CostInvoicePolicy.new(__getobj__, B2B::CostInvoice)
  end

  def invoice_policy
    @invoice_policy ||= InvoicePolicy.new(__getobj__, Invoice)
  end

  def workers_report_policy
    @workers_report_policy ||= WorkersReportPolicy.new(__getobj__, EmployeesReport)
  end

  # NOTE: zauwaz ze jest User zamiast HolidayRequest i nie powoduje to bledu
  def holiday_request_policy
    @holiday_request_policy ||= HolidayRequestPolicy.new(__getobj__, User)
  end

  def request_tracker_issue_policy
    @request_tracker_issue_policy ||= RequestTrackerIssuePolicy.new(__getobj__, User)
  end

  def training_request_policy
    @training_request_policy ||= TrainingRequestPolicy.new(__getobj__, TrainingRequest)
  end

  def docs_file_policy
    @docs_file_policy ||= DocsFilePolicy.new(__getobj__, DocsFile)
  end

  def dashboard_policy
    @dashboard_policy ||= DashboardPolicy.new(__getobj__, Dashboard)
  end

  def calendar_month_policy
    @calendar_month_policy ||= CalendarMonthPolicy.new(__getobj__, CalendarMonth)
  end

  def remote_work_period_policy
    @remote_work_period_policy ||= RemoteWorkPeriodPolicy.new(__getobj__, RemoteWorkPeriod)
  end

  def external_cost_policy
    @external_cost_policy ||= ExternalCostPolicy.new(__getobj__, ExternalCost)
  end

  def registry_activity_policy
    @registry_activity_policy ||= RegistryActivityPolicy.new(__getobj__, RegistryActivity)
  end

  def deletion_request_policy
    @deletion_request_policy ||= DeletionRequestPolicy.new(__getobj__, DeletionRequest)
  end

  def allowed_activity?(activity)
    __getobj__.global_roles.map(&:activities).flatten.uniq.include?(activity)
  end
end
