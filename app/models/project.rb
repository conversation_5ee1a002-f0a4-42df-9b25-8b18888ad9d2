class Project < ApplicationRecord
  include Documentation::ProjectModel
  include RedmineSynchronizable
  include ProjectClickUpSynchronizable
  include ProjectLdapEntryable if Settings.ldap_provider.present?
  include PaymentSchedulable
  include PublicActivity::Model

  tracked owner: ->(controller, _model) { controller.current_user if controller }, only: :create

  RM_ATTRIBUTES = %i[name identifier description homepage public parent_id inherit_members
                     company_id owncloud code_name accounting_number_id sla cooperative_project].freeze
  CU_ATTRIBUTES = %i[name cooperative_project].freeze

  acts_as_nested_set dependent: :destroy

  belongs_to :company, optional: true
  belongs_to :parent, class_name: 'Project', inverse_of: :children, touch: true, optional: true
  belongs_to :responsible, class_name: 'User', optional: true
  belongs_to :author, class_name: 'User', optional: true
  belongs_to :accounting_number, optional: true
  belongs_to :bank_account, optional: true

  has_many :children, -> { order(:status) }, class_name: 'Project', foreign_key: 'parent_id', inverse_of: :parent
  has_many :memberships, autosave: true
  # Notice that after_destroy callbacks will not be triggered on the associated entity when using has_many :through
  # If the :through option is true callbacks in the join models are triggered except destroy callbacks, since deletion is direct.
  has_many :membership_roles, through: :memberships,
                              after_remove: %i[touch_self touch_membership_role],
                              after_add: %i[touch_self touch_membership_role]
  has_many :project_agreements, dependent: :destroy
  has_many :approvals, through: :project_agreements
  has_many :invoices, through: :payments
  has_many :docs_files, dependent: :destroy

  has_many :payment_mpk_positions
  has_many :external_costs, dependent: :destroy
  has_many :shared_payments, through: :payment_mpk_positions, source: :payment

  has_paper_trail on: %i[create update destroy]
  accepts_nested_attributes_for :project_agreements

  has_many :assets
  has_many :issues, dependent: :destroy
  has_many :resource_time_entries, dependent: :destroy
  has_many :spent_time_entries
  has_many :scheduled_time_entries
  has_many :deletion_request_projects, dependent: :destroy

  validates :identifier, presence: true,
                         uniqueness: { case_sensitive: false },
                         format: { with: /\A[a-z][a-z0-9\-_]*\z/,
                                   if: proc { |record| record.identifier_changed? },
                                   message: 'cannot be a number' },
                         length: { in: 1..100 },
                         exclusion: { in: %w[new] }

  validate :personal_data_cannot_change_to_false
  validate :company_change
  validate :check_accounting_number, on: %i[create update]

  validates :name, presence: true
  validates :accounting_number_id, presence: true
  validate :accounting_number_not_locked

  validates :company_id,
            presence: { if: -> { new_record? || company_id_changed? } }
  validate :company_bank_account
  validate :payment_schedule_required_validation, if: -> { invoices.live.any? }

  validates :sla_start_date, presence: true, if: :sla?
  validates :sla_start_date, date: { before_or_equal_to: :sla_end_date },
                             if: -> { sla? && sla_end_date? && sla_start_date_changed? }
  validates :sla_end_date, date: { after_or_equal_to: :sla_start_date, allow_blank: true },
                           if: -> { sla? && sla_end_date_changed? }
  validates :cooperative_project, exclusion: { in: [true] },
                                  if: ->(project) { project.parent.present? && !project.parent.cooperative_project? }

  before_validation :reset_sla_dates, unless: :sla?

  scope :status, ->(arg) { where(arg.blank? ? nil : { status: arg.to_i }) }

  attr_writer :copied

  scope :of_group, lambda { |group|
    where \
      .not(status: statuses[:archived]) \
      .joins(:memberships) \
      .where(memberships: { member_id: group.id, member_type: 'Group' }) \
      .distinct
  }

  scope :of_user, lambda { |user|
    left_joins(:memberships).where(memberships: { member: user })
  }

  scope :native, -> { joins(:company).where(companies: { name: Company::NATIVE }) }
  scope :idle, -> { where(idle: true) }
  scope :unsupervised, lambda {
    joins(<<~SQL.squish
      LEFT JOIN memberships ON memberships.project_id = projects.id
        AND memberships.member_type = "User"
      LEFT JOIN membership_roles
        ON membership_roles.membership_id = memberships.id
      LEFT JOIN roles ON membership_roles.role_id = roles.id
        AND roles.responsible = 1
      LEFT JOIN users ON users.id = memberships.member_id AND users.state = 0
    SQL
         ).where(roles: { id: nil })
  }
  scope :supervised_by, lambda { |user|
    joins(memberships: :roles).where(roles: { responsible: true },
                                     memberships: { member_type: 'User', member_id: user.id })
  }

  before_update :update_inherited_members,
                if: -> { (inherit_members_changed? || parent_id_changed?) }
  before_create :set_inherited_members, if: :inherit_members?
  before_destroy :destroy_memberships
  before_destroy :remove_cooperative_child_project, if: :cooperative_project?
  after_create :add_default_roles
  after_update :create_owncloud_directory, if: lambda {
    owncloud? && saved_change_to_owncloud?
  }
  after_update :create_docscloud_directory, if: lambda {
    docs_cloud? && saved_change_to_docs_cloud?
  }
  after_create :create_owncloud_directory, if: :owncloud?
  after_create :create_docscloud_directory, if: :docs_cloud?
  after_save :turn_off_rodo, if: :can_turn_off_rodo_for_project?
  after_save :remove_payment_schedule, unless: :payment_schedule_required?

  enum status: { active: 0, closed: 1, archived: 2 }

  def account_number
    accounting_number&.number
  end

  def company_changable?
    invoices.accepted_or_issued.empty?
  end

  def holiday_projects?
    Settings.holiday_projects.present? && Settings.holiday_projects.include?(id)
  end

  def holiday_accounting_numbers?
    Settings.holiday_accounting_numbers.present? && Settings.holiday_accounting_numbers.include?(accounting_number&.number)
  end

  def add_user_to_project(user_id, role_id, auto_created: false)
    membership = memberships.find_or_initialize_by(member_type: 'User',
                                                   member_id: user_id.to_s)
    update_roles(membership, role_id)
    membership.save!

    return unless auto_created

    membership.membership_roles.each do |membership_role|
      membership_role.update(auto_created: auto_created) if membership_role.role_id.to_i == role_id.to_i
    end
  end

  def add_default_roles
    add_default_users
    add_default_groups
    add_management_users
  end

  private

  def personal_data_cannot_change_to_false
    return unless personal_data_changed? && personal_data_was && !approvals.where(accepted: true).count.zero?

    errors.add(:personal_data, :cannot_be_changed)
  end

  def company_change
    return unless company_id_changed? && !company_changable?

    errors.add(:company_id, :cannot_be_changed)
  end

  def create_owncloud_directory
    OwncloudWorker.perform_in(1.minute, 'create_directory', identifier, 31)
  end

  def create_docscloud_directory
    DocscloudWorker.perform_in(1.minute, 'create_directory', identifier, 31)
  end

  def rm_attr_changed?
    return false if @copied

    super
  end

  def default_users_hash
    @default_users_hash ||= Department.includes(:users).where.not(role_id: nil).map do |department|
      department.users.select(&:active?).map { |user| [user.id, department.role_id] }
    end.flatten(1).to_h
  end

  def add_default_users
    departments = Department.where.not(role_id: nil)

    departments = if company.name == 'Artegence'
                    departments.where("name LIKE '%Artegence%'")
                  else
                    departments.where("name NOT LIKE '%Artegence%'")
                  end

    departments.map do |department|
      role_id = department.role_id

      department.users.each do |user|
        add_user_to_project(user.id, role_id)
      end
    end
  end

  def add_default_groups
    default_groups_hash = Settings.default_groups.to_h
    return unless default_groups_hash

    default_groups_hash.each do |group_id, role_ids|
      membership = memberships.find_or_initialize_by(member_type: 'Group',
                                                     member_id: group_id.to_s)
      update_roles(membership, role_ids)
      membership.save!
    end
  end

  def add_management_users
    default_management_role = Settings.default_management_role
    role_id = Role.find_or_create_by!(name: default_management_role)&.id if default_management_role.present?
    board_members = User.board_members.active

    return unless board_members.present? && role_id.present?

    board_members.each do |user|
      add_user_to_project(user.id, role_id)
    end
  end

  def update_roles(membership, role_id)
    if membership.new_record?
      membership.role_ids = [role_id].flatten
    else
      membership.membership_roles.build(role_id: role_id)
    end
  end

  def destroy_memberships
    memberships.each do |membership|
      membership.destroy_without_project_touch
    end
  end

  # membership_role # removed membership_role (delete_all/destroy memberships)
  def touch_membership_role(membership_role)
    membership_role.touch if membership_role.persisted?
  end

  # self # project
  def touch_self(_)
    touch if persisted?
  end

  def set_inherited_members
    return unless parent && inherit_members

    parent.memberships.includes(:membership_roles).find_each do |parent_membership|
      current_memberships = memberships.to_a
      membership = current_memberships.detect do |m|
        m.member_id == parent_membership.member_id &&
          m.member_type == parent_membership.member_type
      end || memberships.build(
        member_id: parent_membership.member_id,
        member_type: parent_membership.member_type
      )
      parent_membership.membership_roles.each do |parent_membership_role|
        membership.membership_roles.build(
          role_id: parent_membership_role.role_id,
          inherited_from: parent_membership_role
        )
        membership.save if membership.persisted?
      end
    end
  end

  def check_accounting_number # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
    return if !company&.click_up_synchronization? || cooperative_project?

    # validation only for ClickUp synchronization
    # spaces - should be a project with a zero account number without a parent
    # folders - should be a project with a non-zero account number with a zero account number project as the parent

    project_zero_an = accounting_number&.number&.in?([0, 706])
    parent_zero_an = parent&.accounting_number&.number&.in?([0, 706])

    errors.add(:accounting_number, :invalid_parent) if !project_zero_an && !parent_zero_an
  end

  def update_inherited_members
    membership_roles.where.not(inherited_from_id: nil)
                    .find_each do |membership_role|
      membership_role.destroy
    end
    set_inherited_members
  end

  def archive!
    children.each do |subproject|
      subproject.send :archive!
    end
    update_attribute :status, self.class.statuses[:archived]
  end

  def parent_activity
    return if !parent || parent.active?

    errors.add(:parent, 'Should be active')
  end

  def can_turn_off_rodo_for_project?
    saved_change_to_attribute?(:personal_data) && personal_data_before_last_save &&
      approvals.where(accepted: true).count.zero?
  end

  def turn_off_rodo
    Approval.where(approvable_id: project_agreements.ids, approvable_type: 'ProjectAgreement').destroy_all
    project_agreements.destroy_all
  end

  def company_bank_account
    return unless bank_account
    return if bank_account.company_id == company_id

    errors.add(:bank_account_id, :not_in_company)
  end

  def payment_schedule_required_validation
    return if payment_schedule_required?

    errors.add(:payment_schedule_required, :must_be_true)
  end

  def reset_sla_dates
    self.sla_start_date = nil
    self.sla_end_date = nil
  end

  def accounting_number_not_locked
    return unless accounting_number_id_changed?
    return unless accounting_number&.locked?

    errors.add(:accounting_number_id, :locked)
  end

  def remove_payment_schedule
    payment_schedule&.destroy
  end
end
