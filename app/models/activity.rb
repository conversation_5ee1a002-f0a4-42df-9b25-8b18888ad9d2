class Activity
  CLIENT_ODDITIES = [].freeze
  CLIENT_PARTIAL = [].freeze
  CLIENT = ['public_keys:manage'].freeze
  # (%w( projects ).map do |controller_name|
  #  %w( index show ).map { |action_name| "#{controller_name}:#{action_name}" }
  # end.flatten + CLIENT_ODDITIES + CLIENT_PARTIAL).sort.freeze

  PUBLIC = ['public_keys:manage'].freeze

  ESSENTIAL = [
    'users:index',
    'activities:index',
    'companies:index',
    'companies:show',
    'absences:index',
    'absences:show',
    'absences:create',
    'absences:batch_create',
    'absences:update',
    'absences:batch_update',
    'absences:destroy',
    'absences:batch_destroy',
    'departments:index',
    'holiday_requests:index',
    'holiday_requests:show',
    'holiday_requests:create',
    'holiday_requests:update',
    'holiday_requests:destroy',
    'public_keys:manage',
    'wifi_tokens:create',
    'wifi_tokens:index',
    'request_tracker_issues:create',
    'assets:create',
    'assets:index',
    'dms-cost_invoices:create',
    'contractors:index',
    'contractors:create',
    'mpk_numbers:index',
    'kubernetes_clusters:index',
    'training_requests:access'
  ].freeze

  DEFAULT_ODDITIES = [].freeze
  DEFAULT_PARTIAL = ESSENTIAL + [
    # scoped to specific current_user
    'projects:index',
    'projects:show',
    'agreements:show',
    'project_agreements:show',
    'groups:index',
    'groups:show',
    'memberships:index',
    'memberships:show',
    'whoiswhos:index',
    'whoiswhos:show',
    'whoiswhos:create',
    'whoiswhos:update',
    'whoiswhos:destroy',
    'assets:show'

  ].freeze
  DEFAULT = (DEFAULT_ODDITIES + DEFAULT_PARTIAL).sort.freeze

  GENERAL_FLOW = DEFAULT + [
    'dms-cost_invoices:general_flow'
  ]

  ALL_FLOWS = DEFAULT + [
    'dms-cost_invoices:general_flow',
    'dms-cost_invoices:simplified_flow'
  ]

  HR_MANAGER_ODDITIES = DEFAULT_ODDITIES + [
    'users:show',
    'users:create',
    'users:update',
    'users:global_index',
    'positions:index',
    'positions:show',
    'positions:create',
    'positions:update',
    'positions:destroy',
    'users:resend_confirmation_instructions',
    'groups:update',
    'departments:show',
    'departments:create',
    'departments:update',
    'holiday_requests_users:index',
    'holiday_requests:manage',
    'bios:show',
    'bios:index',
    'bios:create',
    'remote_work_periods:global_manage',
    'training_requests:global_manage',
    'calendar_months:global_manage'
  ].freeze
  HR_MANAGER_PARTIAL = DEFAULT_PARTIAL + [].freeze
  HR_MANAGER = (HR_MANAGER_ODDITIES + HR_MANAGER_PARTIAL).sort.freeze

  COORDINATOR_ODDITIES = HR_MANAGER_ODDITIES + [
    'clients:index',
    'clients:manage',
    'invoices:index',
    'cost_invoices:global_create',
    'payment_schedules:issue',
    'dms-cost_invoices:global_index',
    'dms-cost_invoices:general_flow',
    'dms-cost_invoices:simplified_flow',
    'projects:index_all',
    'calendar_months:global_manage'
  ].freeze

  HR_COORDINATOR_ODDITIES = [
    'cost_invoices:global_index'
  ].freeze

  PROJECT_MANAGER_ODDITIES = DEFAULT_ODDITIES + [
    # skip to check per project:
    'roles:index',
    'roles:show',
    'projects:add_subprojects',
    'projects:update',
    'projects:close',
    'projects:reopen',
    'memberships:create',
    'memberships:update',
    'memberships:destroy',
    'payment_schedules:show',
    'payment_schedules:manage',
    'clients:index',
    'project_agreements:index',
    'project_agreements:show',
    'project_agreements:selected',
    'project_agreements:update',
    'approvals:index',
    'assets:index',
    'assets:create',
    'assets:manage',
    'assets:show',
    'assets:allow_execution',
    'docs_files:index',
    'invoices:index',
    'invoices:index'
  ].freeze
  PROJECT_MANAGER_PARTIAL = DEFAULT_PARTIAL + [].freeze
  PROJECT_MANAGER = (PROJECT_MANAGER_ODDITIES + PROJECT_MANAGER_PARTIAL).sort.freeze

  UBER_PROJECT_MANAGER_ODDITIES = PROJECT_MANAGER_ODDITIES + [
    # uber project manager specific:
    'projects:create',
    'projects:copy_source',
    'projects:copy',
    'users:show'
  ].freeze
  UBER_PROJECT_MANAGER_PARTIAL = PROJECT_MANAGER_PARTIAL + [].freeze
  UBER_PROJECT_MANAGER = (UBER_PROJECT_MANAGER_ODDITIES + UBER_PROJECT_MANAGER_PARTIAL).sort.freeze

  TRAFFIC = UBER_PROJECT_MANAGER + [
    'holiday_requests_users:index',
    'holiday_requests:history',
    'remote_work_periods:history',
    'bios:show',
    'bios:index',
    'bios:create'
  ].uniq.freeze

  CONTROLLING_READER = UBER_PROJECT_MANAGER + [
    'dms-cost_invoices:global_index',
    'payment_schedules:global_show',
    'invoices:index'
  ].freeze

  ACCOUNTING_READER = UBER_PROJECT_MANAGER + UBER_PROJECT_MANAGER_ODDITIES + [
    'cost_invoices:global_index',
    'users:global_index',
    'users:show',
    'payment_schedules:global_show',
    'payment_schedules:global_index',
    'invoices:index',
    'contractors:index',
    'projects:report',
    'statistics:view',
    'dms-cost_invoices:global_index'
  ]

  ACCOUNTING_ODDITIES = UBER_PROJECT_MANAGER_ODDITIES + [
    'cost_invoices:global_manage',
    'payment_schedules:global_manage',
    'payment_schedules:global_show',
    'payment_schedules:manage_after_date',
    'contractors:manage',
    'contractors:destroy',
    'projects:report',
    'projects:global_show',
    'invoices:index',
    'cost_invoices:global_create',
    'dms-cost_invoices:global_manage',
    'dms-cost_invoices:general_flow',
    'dms-cost_invoices:simplified_flow',
    'clients:manage',
    'accounting_numbers:manage',
    'docs_files:global_manage',
    'docs_files:index',
    'statistics:view',
    'remote_work_periods:global_manage'
  ].freeze

  ACCOUNTING = UBER_PROJECT_MANAGER + ACCOUNTING_ODDITIES

  PROJECT_COORDINATOR_ODDITIES = UBER_PROJECT_MANAGER_ODDITIES + [
    'payment_schedules:global_show',
    'contractors:index',
    'projects:global_show',
    'projects:global_manage'
  ]

  PROJECT_COORDINATOR = UBER_PROJECT_MANAGER + PROJECT_COORDINATOR_ODDITIES

  AGREEMENTS_ADMIN = DEFAULT_ODDITIES +
                     %w[
                       agreements:index
                       agreements:show
                       agreements:create
                       agreements:edit
                       agreements:update
                       agreements:destroy
                       agreements:form_data
                       project_agreements:index
                       project_agreements:show
                       project_agreements:selected
                       project_agreements:update
                       approvals:index
                       approvals:update
                       registry_activity:manage
                       deletion_request:manage
                     ].freeze

  ADMIN_ODDITIES = DEFAULT_ODDITIES + AGREEMENTS_ADMIN + [
    'holiday_requests:manage',
    'global_roles:index',
    'global_roles:show',
    'roles:index',
    'roles:show',
    'roles:create',
    'roles:update',
    'projects:add_subprojects',
    'projects:create',
    'projects:copy_source',
    'projects:copy',
    'projects:update',
    'projects:close',
    'projects:reopen',
    'projects:report',
    'projects:index_all',
    'memberships:create',
    'memberships:update',
    'memberships:destroy',
    # 'departments:index',
    'departments:show',
    'departments:create',
    'departments:update',
    'departments:destroy',
    # admin not required, but preferred:
    'groups:destroy',
    'roles:destroy',
    'groups:create',
    'groups:update',
    'users:show',
    'users:create',
    'users:update',
    'users:global_index',
    'users:destroy',
    'users:update_password',
    'users:resend_confirmation_instructions',
    'payment_schedules:manage',
    'payment_schedules:show',
    'payment_schedules:manage_after_date',
    'positions:index',
    'positions:show',
    'positions:create',
    'positions:update',
    'positions:destroy',
    # admin specific:
    'projects:destroy',
    'projects:archive',
    'projects:unarchive',
    'clients:manage',
    'clients:index',
    'contractors:manage',
    'contractors:destroy',
    'holiday_requests:history',
    'remote_work_periods:history',
    'users:history',
    'holiday_requests_users:index',
    'booking_resources:index',
    'booking_resources:create',
    'booking_resources:update',
    'booking_resources:show',
    'booking_resources:destroy',
    'booking_resources:form_data',
    'api_keys:index',
    'api_keys:create',
    'api_keys:destroy',
    'public_activities:index',
    'assets:index',
    'assets:create',
    'assets:manage',
    'assets:show',
    'assets:manage_all',
    'projects:public_activities',
    'bios:create',
    'bios:show',
    'bios:index',
    'invoices:index',
    'cost_invoices:global_create',
    'assets:orders',
    'inventory_items:manage',
    'dms-cost_invoices:general_flow',
    'dms-cost_invoices:simplified_flow',
    'accounting_numbers:manage',
    'dms-cost_invoices:global_index',
    'docs_files:global_manage',
    'docs_files:index',
    'dms-cost_invoices:global_manage',
    'remote_work_periods:global_manage',
    'training_requests:global_manage',
    'calendar_months:global_manage',
    'documents:manage_accepted',
    'logs:index',
    'registry_activity:manage',
    'deletion_request:manage'
  ].freeze
  ADMIN_PARTIAL = DEFAULT_PARTIAL + [].freeze
  ADMIN = (ADMIN_ODDITIES + ADMIN_PARTIAL).sort.freeze

  PROGRAMMER_ODDITIES = ADMIN_ODDITIES + [].freeze
  PROGRAMMER_PARTIAL = ADMIN_PARTIAL + [
    # programmer specific:
    'global_roles:create',
    'global_roles:update',
    'global_roles:destroy',
    'companies:create',
    'companies:update',
    'companies:destroy',
    'cost_invoices:global_manage',
    'payment_schedules:global_manage',
    'payment_schedules:global_show',
    'payment_schedules:issue',
    'assets:allow_execution',
    'cost_invoices:global_create',
    'projects:global_show',
    'projects:global_manage',
    'dms-cost_invoices:global_index',
    'statistics:view'
  ].freeze
  PROGRAMMER = (PROGRAMMER_ODDITIES + PROGRAMMER_PARTIAL).sort.freeze

  ASSET_MANAGER = %w[
    assets:index
    assets:create
    assets:manage
    assets:show
    assets:manage_all
    assets:orders
    assets:allow_execution
  ].freeze

  attr_accessor :name

  def self.agreements_admin
    AGREEMENTS_ADMIN
  end

  def self.available
    DEFAULT
  end

  def self.default
    DEFAULT
  end

  def self.general_flow
    GENERAL_FLOW
  end

  def self.all_flows
    ALL_FLOWS
  end

  def self.hr_manager
    HR_MANAGER
  end

  def self.global_traffic
    TRAFFIC
  end

  def self.project_manager
    PROJECT_MANAGER
  end

  def self.uber_project_manager
    UBER_PROJECT_MANAGER
  end

  def self.controlling_reader
    CONTROLLING_READER
  end

  def self.accounting
    ACCOUNTING
  end

  def self.project_coordinator
    PROJECT_COORDINATOR
  end

  def self.programmer
    PROGRAMMER
  end

  def self.admin
    ADMIN
  end

  def self.client
    CLIENT
  end

  def self.coordinator
    (DEFAULT + COORDINATOR_ODDITIES).uniq
  end

  def self.hr_coordinator
    (DEFAULT + COORDINATOR_ODDITIES + HR_COORDINATOR_ODDITIES).uniq
  end

  def self.asset_manager
    DEFAULT + ASSET_MANAGER
  end

  def self.accounting_reader
    ACCOUNTING_READER
  end

  def self.all
    (CLIENT + DEFAULT + PROJECT_MANAGER + UBER_PROJECT_MANAGER + ADMIN + PROGRAMMER +
     HR_MANAGER_ODDITIES + ACCOUNTING_ODDITIES + TRAFFIC + COORDINATOR_ODDITIES +
     PROJECT_COORDINATOR_ODDITIES + ACCOUNTING_READER).uniq.sort
  end

  def self.none
    []
  end

  def initialize(attributes)
    @name = attributes[:name].to_s if attributes[:name].present?
  end

  delegate :to_s, to: :name

  def model_name
    self.class.name
  end
end
